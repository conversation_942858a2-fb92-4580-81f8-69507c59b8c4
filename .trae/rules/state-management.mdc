---
description:
globs:
alwaysApply: false
---
# 状态管理规则

## Pinia使用规范

### Store定义
```typescript
// 定义store
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    isLogin: false,
  }),

  getters: {
    userName: (state) => state.userInfo?.name || '',
  },

  actions: {
    async login(credentials) {
      // 登录逻辑
    },
    logout() {
      // 登出逻辑
    }
  }
})
```

### 持久化配置
使用配置在 [src/store/persist.ts](mdc:src/store/persist.ts) 的持久化选项：
```typescript
export const useUserStore = defineStore('user', {
  // store定义...
}, {
  persist: {
    key: 'user-store',
    storage: 'localStorage', // 或 'sessionStorage'
    paths: ['userInfo', 'isLogin'] // 指定需要持久化的字段
  }
})
```

### Store使用
```vue
<script setup lang="ts">
// 在组件中使用
const userStore = useUserStore()

// 响应式解构
const { userInfo, isLogin } = storeToRefs(userStore)

// 调用actions
const handleLogin = () => {
  userStore.login(credentials)
}
</script>
```

### 组合式Store
```typescript
// 组合式写法
export const useCounterStore = defineStore('counter', () => {
  const count = ref(0)
  const doubleCount = computed(() => count.value * 2)

  function increment() {
    count.value++
  }

  return { count, doubleCount, increment }
})
```

### Store分类规范
1. **用户状态**: `useUserStore` - 用户信息、登录状态
2. **应用配置**: `useAppStore` - 主题、语言、系统设置
3. **业务数据**: `useBusinessStore` - 具体业务相关状态
4. **临时状态**: 使用 `ref`/`reactive` 或组件内部状态

### 最佳实践
1. Store命名使用 `use[Name]Store` 格式
2. 合理拆分Store，避免单个Store过大
3. 使用 TypeScript 提供完整类型定义
4. 重要数据配置持久化存储
5. Actions中处理异步逻辑和副作用
6. 使用 `storeToRefs` 解构响应式数据
7. 避免在Store中直接操作DOM

### 与API集成
```typescript
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    loading: false,
  }),

  actions: {
    async fetchUserInfo() {
      this.loading = true
      try {
        // 使用Alova API
        const response = await Apis.user.getUserInfo()
        this.userInfo = response
      } catch (error) {
        console.error('获取用户信息失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
})
```
