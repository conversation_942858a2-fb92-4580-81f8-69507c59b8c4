---
description:
globs:
alwaysApply: false
---
# 组件化开发规则

## 组件使用优先级
1. 优先使用 `src/components/` 目录下的项目组件
2. 其次使用 `wot-design-uni` 组件库组件
3. 最后考虑原生 uni-app 组件
4. Toast、Loading和MessageBox交互使用 `src/components/` 目录下提供的全局GlobalToast、GlobalLoading、GlobalMessage组件实现

## wot-design-uni 组件库
1. 组件库文档:https://wot-design-uni.cn/llms-full.txt
2. 优先使用组件库提供的主题和样式
3. 遵循组件库的最佳实践和 API 规范

## 自定义组件开发
1. 通用组件放在 `src/components/`
2. 业务组件放在 `src/business/`
3. 组件要支持主题切换和多端适配
4. 提供完整的 TypeScript 类型定义

## 组件命名
1. 自定义组件使用 PascalCase
2. 组件文件名使用 kebab-case

## 样式
使用 unocss 编写样式
